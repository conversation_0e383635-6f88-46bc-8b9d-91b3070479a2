import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

export default function Deals() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <Search />
        <div className='ml-auto flex items-center gap-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Content ===== */}
      <Main fixed>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>
            Deals (<PERSON><PERSON> hội bán hàng)
          </h1>
          <p className='text-muted-foreground'>
            Quản lý cơ hội bán hàng theo pipeline 5 giai đoạn cố định, ưu tiên B2C.
          </p>
        </div>
        
        <div className='mt-8 flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <h3 className='text-lg font-medium text-muted-foreground mb-2'>
              Module Deals đang được phát triển
            </h3>
            <p className='text-sm text-muted-foreground'>
              Tính năng sẽ được triển khai theo tài liệu MVP
            </p>
          </div>
        </div>
      </Main>
    </>
  )
}
